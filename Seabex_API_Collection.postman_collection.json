{"info": {"name": "Seabex API - Irrigation Tools", "description": "Collection for testing Seabex irrigation API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-authenticate before each request", "if (!pm.environment.get(\"access_token\") || Date.now() > pm.environment.get(\"token_expires_at\")) {", "    pm.sendRequest({", "        url: pm.environment.get(\"base_url\") + \"/oauth/token\",", "        method: \"POST\",", "        header: { \"Content-Type\": \"application/x-www-form-urlencoded\" },", "        body: {", "            mode: \"urlencoded\",", "            urlencoded: [", "                {key: \"grant_type\", value: \"client_credentials\"},", "                {key: \"client_id\", value: pm.environment.get(\"client_id\")},", "                {key: \"client_secret\", value: pm.environment.get(\"client_secret\")},", "                {key: \"scope\", value: \"magonia-api\"}", "            ]", "        }", "    }, function (err, response) {", "        if (response.code === 200) {", "            const jsonData = response.json();", "            pm.environment.set(\"access_token\", jsonData.access_token);", "            pm.environment.set(\"token_expires_at\", Date.now() + (jsonData.expires_in * 1000));", "        }", "    });", "}", "", "// Generate cache buster", "const cacheBuster = new Date().toISOString() + \"_\" + Math.random().toString(36).substr(2, 8);", "pm.environment.set(\"cache_buster\", cacheBuster);"]}}], "item": [{"name": "Authentication", "item": [{"name": "Get Access Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials"}, {"key": "client_id", "value": "{{client_id}}"}, {"key": "client_secret", "value": "{{client_secret}}"}, {"key": "scope", "value": "magonia-api"}]}, "url": {"raw": "{{base_url}}/oauth/token", "host": ["{{base_url}}"], "path": ["o<PERSON>h", "token"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    pm.environment.set(\"access_token\", jsonData.access_token);", "    pm.environment.set(\"token_expires_at\", Date.now() + (jsonData.expires_in * 1000));", "    pm.test(\"Access token obtained\", function () {", "        pm.expect(jsonData.access_token).to.not.be.undefined;", "    });", "}"]}}]}]}, {"name": "Irrigation Tools", "item": [{"name": "Do I need to irrigate today?", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{user_id}}\",\n  \"_cache_buster\": \"{{cache_buster}}\"\n}"}, "url": {"raw": "{{base_url}}/api/magonia/tools/irrigations/check_today_active_irrigation_user", "host": ["{{base_url}}"], "path": ["api", "magonia", "tools", "irrigations", "check_today_active_irrigation_user"]}}}, {"name": "Check specific field irrigation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{user_id}}\",\n  \"field_name\": \"CHlewi\",\n  \"date_of_calculation\": \"2024-01-15\",\n  \"_cache_buster\": \"{{cache_buster}}\"\n}"}, "url": {"raw": "{{base_url}}/api/magonia/tools/irrigations/check_irrigation_user_data", "host": ["{{base_url}}"], "path": ["api", "magonia", "tools", "irrigations", "check_irrigation_user_data"]}}}, {"name": "Check future irrigation dates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{user_id}}\",\n  \"calculation_date\": \"2024-01-20\",\n  \"_cache_buster\": \"{{cache_buster}}\"\n}"}, "url": {"raw": "{{base_url}}/api/magonia/tools/irrigations/check_earliest_irrigation_dates", "host": ["{{base_url}}"], "path": ["api", "magonia", "tools", "irrigations", "check_earliest_irrigation_dates"]}}}, {"name": "Calculate total irrigation volume (next X days)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{user_id}}\",\n  \"days\": 7,\n  \"_cache_buster\": \"{{cache_buster}}\"\n}"}, "url": {"raw": "{{base_url}}/api/magonia/tools/irrigations/calculate_total_irrigation_volume_next_x_days", "host": ["{{base_url}}"], "path": ["api", "magonia", "tools", "irrigations", "calculate_total_irrigation_volume_next_x_days"]}}}, {"name": "Check highest irrigation requirement", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{user_id}}\",\n  \"_cache_buster\": \"{{cache_buster}}\"\n}"}, "url": {"raw": "{{base_url}}/api/magonia/tools/irrigations/check_highest_irrigation_requirement", "host": ["{{base_url}}"], "path": ["api", "magonia", "tools", "irrigations", "check_highest_irrigation_requirement"]}}}]}], "variable": [{"key": "base_url", "value": "https://back.seabex.com"}]}