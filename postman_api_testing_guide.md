# 🚀 Seabex API Testing with Postman

## 📋 Prerequisites

You'll need:
- **SEABEX_CLIENT_ID** and **SEABEX_CLIENT_SECRET** (from your environment variables)
- **User ID**: `f68381cd-a748-47bd-842c-701790b35e3c` (example from codebase)
- Postman application

## 🔐 Step 1: Get Access Token

### Request Details
- **Method**: `POST`
- **URL**: `https://back.seabex.com/oauth/token`
- **Headers**:
  ```
  Content-Type: application/x-www-form-urlencoded
  ```

### Body (form-data)
```
grant_type: client_credentials
client_id: YOUR_SEABEX_CLIENT_ID
client_secret: YOUR_SEABEX_CLIENT_SECRET
scope: magonia-api
```

### Expected Response
```json
{
  "token_type": "Bearer",
  "expires_in": 3600,
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."
}
```

## 🎯 Step 2: Test "Do I need to irrigate today?" API

### Request Details
- **Method**: `POST`
- **URL**: `https://back.seabex.com/api/magonia/tools/irrigations/check_today_active_irrigation_user`

### Headers
```
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
Accept: application/json
```

### Body (JSON)
```json
{
  "user_id": "f68381cd-a748-47bd-842c-701790b35e3c",
  "_cache_buster": "2024-01-15T10:30:45.123456_abc12345"
}
```

### Expected Responses

#### ✅ Fields Need Irrigation
```json
{
  "data": {
    "count": 2,
    "total_irrigation_volume": 150.5,
    "areas": [
      {
        "area_name": "Field_A",
        "irrigation_volume": 75.2
      },
      {
        "area_name": "Field_B",
        "irrigation_volume": 75.3
      }
    ]
  }
}
```

#### ❌ No Irrigation Needed
```json
{
  "data": {
    "count": 0,
    "total_irrigation_volume": 0,
    "areas": []
  }
}
```

#### 🚫 Error Response
```json
{
  "error": "Authentication failed"
}
```

## 🔧 Step 3: Other Available Irrigation Endpoints

### 1. Check Specific Field Irrigation
- **URL**: `https://back.seabex.com/api/magonia/tools/irrigations/check_irrigation_user_data`
- **Body**:
```json
{
  "user_id": "f68381cd-a748-47bd-842c-701790b35e3c",
  "field_name": "CHlewi",
  "date_of_calculation": "2024-01-15",
  "_cache_buster": "2024-01-15T10:30:45.123456_abc12345"
}
```

### 2. Check Future Irrigation Fields
- **URL**: `https://back.seabex.com/api/magonia/tools/irrigations/check_earliest_irrigation_dates`
- **Body**:
```json
{
  "user_id": "f68381cd-a748-47bd-842c-701790b35e3c",
  "calculation_date": "2024-01-20",
  "_cache_buster": "2024-01-15T10:30:45.123456_abc12345"
}
```

### 3. Calculate Total Irrigation Volume
- **URL**: `https://back.seabex.com/api/magonia/tools/irrigations/calculate_total_irrigation_volume_next_x_days`
- **Body**:
```json
{
  "user_id": "f68381cd-a748-47bd-842c-701790b35e3c",
  "days": 7,
  "_cache_buster": "2024-01-15T10:30:45.123456_abc12345"
}
```

## 📝 Postman Collection Setup

### Environment Variables
Create a Postman environment with:
```
base_url: https://back.seabex.com
client_id: YOUR_SEABEX_CLIENT_ID
client_secret: YOUR_SEABEX_CLIENT_SECRET
user_id: f68381cd-a748-47bd-842c-701790b35e3c
access_token: (will be set after authentication)
```

### Pre-request Script for Authentication
Add this to your collection's pre-request script:
```javascript
// Check if access token exists and is not expired
if (!pm.environment.get("access_token") || 
    Date.now() > pm.environment.get("token_expires_at")) {
    
    // Get new access token
    pm.sendRequest({
        url: pm.environment.get("base_url") + "/oauth/token",
        method: "POST",
        header: {
            "Content-Type": "application/x-www-form-urlencoded"
        },
        body: {
            mode: "urlencoded",
            urlencoded: [
                {key: "grant_type", value: "client_credentials"},
                {key: "client_id", value: pm.environment.get("client_id")},
                {key: "client_secret", value: pm.environment.get("client_secret")},
                {key: "scope", value: "magonia-api"}
            ]
        }
    }, function (err, response) {
        if (response.code === 200) {
            const jsonData = response.json();
            pm.environment.set("access_token", jsonData.access_token);
            pm.environment.set("token_expires_at", Date.now() + (jsonData.expires_in * 1000));
        }
    });
}

// Generate cache buster
const cacheBuster = new Date().toISOString() + "_" + Math.random().toString(36).substr(2, 8);
pm.environment.set("cache_buster", cacheBuster);
```

## 🎯 Quick Test Steps

1. **Import Environment**: Set up your Postman environment with the variables above
2. **Test Authentication**: Send the OAuth token request first
3. **Copy Access Token**: Use the returned token for subsequent requests
4. **Test Irrigation API**: Send the irrigation check request
5. **Verify Response**: Check that you get the expected JSON structure

## 🔍 Troubleshooting

### Common Issues:
- **401 Unauthorized**: Check your client_id and client_secret
- **403 Forbidden**: Verify the scope is set to "magonia-api"
- **Empty Response**: Ensure user_id is correct and user has fields
- **Token Expired**: Get a new access token (expires in 1 hour)

### Debug Tips:
- Always include the `_cache_buster` parameter for fresh data
- Check that `user_id` matches an existing user in the system
- Verify the `Content-Type` header is set to `application/json`
- Use the exact URL format shown above
