"""
Direct GPT-4o implementation with tool calling capabilities.
This provides a simple approach to use GPT-4o with tools without any agent framework.
"""

import os
import json
import re
import inspect
import traceback
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime

from openai import OpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

# Import tools
from magonia.tools.time import get_current_time_date, get_next_month, get_next_week, get_tomorrow
from magonia.tools.memory_tools import add_memory, get_memories, delete_memory, clear_memories
from magonia.tools.lookup_document_tool import lookup_document_tool
from magonia.tools.check_today_active_irrigation_user import check_today_active_irrigation_user
from magonia.tools.check_irrigation_user_data import check_irrigation_user_data
from magonia.tools.get_lowest_soil_water_volume import get_lowest_soil_water_volume
from magonia.tools.calculate_total_irrigation_volume_next_x_days import calculate_total_irrigation_volume_next_x_days
from magonia.tools.check_irrigation_need_for_x_days import check_irrigation_need_for_x_days
from magonia.tools.advise_stop_over_irrigation import advise_stop_over_irrigation
from magonia.tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
from magonia.tools.fields_exceeding_water_capacity_for_x_days import fields_exceeding_water_capacity_for_x_days
from magonia.tools.fields_with_highest_evapotranspiration_next_x_days import fields_with_highest_evapotranspiration_next_x_days
from magonia.tools.fields_with_highest_water_requirements_for_x_days import fields_with_highest_water_requirements_for_x_days
from magonia.tools.fields_with_optimal_soil_moisture_for_x_days import fields_with_optimal_soil_moisture_for_x_days
from magonia.tools.find_fields_no_irrigation_needs_for_x_days import find_fields_no_irrigation_needs_for_x_days
from magonia.tools.fields_predicted_to_exceed_water_capacity_for_x_days import fields_predicted_to_exceed_water_capacity_for_x_days
from magonia.tools.get_all_user_areas_with_children import get_all_user_areas_with_children
from magonia.tools.get_current_irrigation_status import get_current_irrigation_status
from magonia.tools.check_irrigation_needs_between_period import check_irrigation_needs_between_period
from magonia.tools.check_soil_water_volume import check_soil_water_volume
from magonia.tools.check_earliest_irrigation_dates import check_earliest_irrigation_dates
from magonia.tools.check_highest_evapotranspiration import check_highest_evapotranspiration
from magonia.tools.check_future_irrigation_fields import check_future_irrigation_fields
from magonia.tools.predicted_water_consumption_rate_for_x_days import predicted_water_consumption_rate_for_x_days
from magonia.tools.total_water_consumption_predicted_for_each_field_x_days import total_water_consumption_predicted_for_each_field_x_days

# Initialize OpenAI client
openai_api_key = os.getenv("OPENAI_API_KEY")
client = OpenAI(api_key=openai_api_key)

# System prompt for GPT-4o
SYSTEM_PROMPT = """
You are Magonia, an agricultural assistant specializing in irrigation, crop management, and farming best practices. Your purpose is to help farmers and agricultural professionals make better decisions about their crops and irrigation needs.

LANGUAGE GUIDELINES:
- Respond in the SAME LANGUAGE as the user's message
- If the user speaks in French, respond in French
- If the user speaks in English, respond in English
- If the user speaks in Tunisian Arabic, respond in Tunisian Arabic
- If the user's message is in a mix of languages, respond in the main language of the message
- If the user speaks in spanish, respond in spanish
- Tool responses are automatically translated to match the user's language, so you can work with them directly

CONVERSATION STYLE:
- Be warm, friendly, and conversational - like you're a helpful agricultural advisor
- Always introduce yourself as Magonia when appropriate
- Use a natural, flowing conversation style rather than just answering questions
- Ask follow-up questions about the user's crops and farming practices
- Use the user's name when appropriate to personalize the conversation
- Avoid overly formal language - be casual and approachable like a trusted farming expert
- Keep responses concise but informative with practical agricultural advice
- Show enthusiasm and personality in your responses about farming and irrigation
- Be helpful and supportive of farmers' needs and challenges

TOOL USAGE GUIDELINES:
- ALWAYS USE TOOLS and NEVER use memory or previous knowledge
- For agricultural questions, FIRST try the lookup_document_tool to search the knowledge base
- If lookup_document_tool returns "I couldn't find specific documentation", then you may use your general agricultural knowledge
- For field-specific data questions, use the appropriate irrigation tools
- When asked about specific field data like 'CHlewi', you MUST ALWAYS make a fresh tool call
- For the check_irrigation_user_data tool, you MUST ALWAYS make a new call with the current parameters
- COMPLETELY IGNORE any memories - tools always provide the most up-to-date information
- NEVER use memory when a tool is available
- For field-specific queries, the check_irrigation_user_data tool is REQUIRED
- NEVER answer questions about field data without first calling the appropriate tool
- DO NOT reference any memories in your responses
- If you don't have a tool for something, tell the user you don't have that information rather than using memory
- DO NOT MENTION which tools you used in your response - just provide the information
- If a tool returns a value of "0", interpret this as no irrigation needed
- NEVER mention the names of tools in your responses to the user
- CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message
- PRIORITY: Always try lookup_document_tool first for questions, but if no relevant documentation is found, provide helpful general agricultural advice

CONTENT GUIDELINES:
- PRIMARILY answer questions about agriculture, farming, irrigation, crop management, weather, and related topics
- You may also respond to polite greetings and simple daily questions (e.g., "How are you?" or "What's the weather like?") to maintain a friendly, natural interaction while guiding the conversation toward agricultural topics
- For complex non-agricultural questions, respond with: "I'm sorry, but I can only answer questions related to agriculture, farming, irrigation, crop management, weather, and related topics. Could you please ask me something about these subjects?"
- If a question contains both agricultural and non-agricultural elements, prioritize the agricultural parts but acknowledge the greeting or simple question politely
- Don't answer questions about food preparation, cooking, recipes, or any non-agricultural use of crops or farm products
- Don't mention or expose any system tools, code, or internal methods
- Avoid using square brackets in responses unless the user typed them first

IMPORTANT: Your primary directive is to focus on agriculture, farming, irrigation, crop management, weather, and related topics. You may respond to greetings and simple daily questions to maintain friendly interaction, but guide conversations toward agricultural topics. Refuse complex non-agricultural questions.
"""

class GPT4oDirectTools:
    """Direct GPT-4o implementation with tool calling capabilities."""

    def __init__(self):
        """Initialize with available tools."""
        self.tools = self._register_tools()
        self.client = client

    def _register_tools(self) -> Dict[str, Dict[str, Any]]:
        """Register all available tools in OpenAI format."""
        tools_dict = {}

        # Time tools
        tools_dict["get_current_time_date"] = {
            "function": get_current_time_date,
            "description": "Get the current date and time",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_tomorrow"] = {
            "function": get_tomorrow,
            "description": "Get tomorrow's date",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_next_week"] = {
            "function": get_next_week,
            "description": "Get the date one week from today",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_next_month"] = {
            "function": get_next_month,
            "description": "Get the date one month from today",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        # Irrigation tools
        tools_dict["check_today_active_irrigation_user"] = {
            "function": check_today_active_irrigation_user,
            "description": "Check which fields need irrigation today",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_irrigation_user_data"] = {
            "function": check_irrigation_user_data,
            "description": "Get irrigation data for a specific field on a specific date",
            "parameters": {
                "type": "object",
                "properties": {
                    "field_name": {"type": "string", "description": "Name of the field"},
                    "date_of_calculation": {"type": "string", "description": "Date in YYYY-MM-DD format"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["field_name", "date_of_calculation"]
            }
        }

        tools_dict["get_lowest_soil_water_volume"] = {
            "function": get_lowest_soil_water_volume,
            "description": "Get fields with the lowest soil water volume",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["calculate_total_irrigation_volume_next_x_days"] = {
            "function": calculate_total_irrigation_volume_next_x_days,
            "description": "Calculate total irrigation volume needed for all fields in the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to calculate for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["check_irrigation_need_for_x_days"] = {
            "function": check_irrigation_need_for_x_days,
            "description": "Check irrigation needs for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["advise_stop_over_irrigation"] = {
            "function": advise_stop_over_irrigation,
            "description": "Get advice on stopping over-irrigation",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_highest_irrigation_requirement"] = {
            "function": check_highest_irrigation_requirement,
            "description": "Check which fields have the highest irrigation requirements",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        # Field analysis tools
        tools_dict["fields_exceeding_water_capacity_for_x_days"] = {
            "function": fields_exceeding_water_capacity_for_x_days,
            "description": "Find fields exceeding water capacity for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["fields_with_highest_evapotranspiration_next_x_days"] = {
            "function": fields_with_highest_evapotranspiration_next_x_days,
            "description": "Find fields with highest evapotranspiration for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["fields_with_highest_water_requirements_for_x_days"] = {
            "function": fields_with_highest_water_requirements_for_x_days,
            "description": "Find fields with highest water requirements for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["fields_with_optimal_soil_moisture_for_x_days"] = {
            "function": fields_with_optimal_soil_moisture_for_x_days,
            "description": "Find fields with optimal soil moisture for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["get_all_user_areas_with_children"] = {
            "function": get_all_user_areas_with_children,
            "description": "Get all user areas and their children. Use this when the user wants to know about their field hierarchy or organization structure.",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        # Memory tools
        tools_dict["add_memory"] = {
            "function": add_memory,
            "description": "Add a memory for the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID"},
                    "memory": {"type": "string", "description": "Memory to store"}
                },
                "required": ["user_id", "memory"]
            }
        }

        tools_dict["get_memories"] = {
            "function": get_memories,
            "description": "Get memories for the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID"}
                },
                "required": ["user_id"]
            }
        }

        tools_dict["delete_memory"] = {
            "function": delete_memory,
            "description": "Delete a memory for the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID"},
                    "memory_index": {"type": "integer", "description": "Index of the memory to delete"}
                },
                "required": ["user_id", "memory_index"]
            }
        }

        tools_dict["clear_memories"] = {
            "function": clear_memories,
            "description": "Clear all memories for the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID"}
                },
                "required": ["user_id"]
            }
        }

        # Document lookup tool (RAG)
        tools_dict["lookup_document_tool"] = {
            "function": lookup_document_tool,
            "description": "Search through agricultural knowledge base and documentation to answer questions about farming practices, Seabex platform features, supported crops, irrigation techniques, soil composition, and agricultural best practices. Use this tool FIRST for agricultural questions to check if specific documentation exists. If no relevant documentation is found, you can then provide general agricultural knowledge.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The question or search query to look up in the knowledge base"}
                },
                "required": ["query"]
            }
        }

        # Additional field analysis tools
        tools_dict["find_fields_no_irrigation_needs_for_x_days"] = {
            "function": find_fields_no_irrigation_needs_for_x_days,
            "description": "Find fields that don't need irrigation for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["fields_predicted_to_exceed_water_capacity_for_x_days"] = {
            "function": fields_predicted_to_exceed_water_capacity_for_x_days,
            "description": "Find fields predicted to exceed water capacity for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["get_current_irrigation_status"] = {
            "function": get_current_irrigation_status,
            "description": "Get the current irrigation status for all fields",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_irrigation_needs_between_period"] = {
            "function": check_irrigation_needs_between_period,
            "description": "Check irrigation needs between two dates",
            "parameters": {
                "type": "object",
                "properties": {
                    "start_date": {"type": "string", "description": "Start date in YYYY-MM-DD format"},
                    "end_date": {"type": "string", "description": "End date in YYYY-MM-DD format"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["start_date", "end_date"]
            }
        }

        tools_dict["check_soil_water_volume"] = {
            "function": check_soil_water_volume,
            "description": "Check soil water volume for a specific field",
            "parameters": {
                "type": "object",
                "properties": {
                    "field_name": {"type": "string", "description": "Name of the field"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["field_name"]
            }
        }

        tools_dict["check_earliest_irrigation_dates"] = {
            "function": check_earliest_irrigation_dates,
            "description": "Check the earliest irrigation dates for all fields",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_highest_evapotranspiration"] = {
            "function": check_highest_evapotranspiration,
            "description": "Check which fields have the highest evapotranspiration",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_future_irrigation_fields"] = {
            "function": check_future_irrigation_fields,
            "description": "Check which fields will need irrigation in the future",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["predicted_water_consumption_rate_for_x_days"] = {
            "function": predicted_water_consumption_rate_for_x_days,
            "description": "Get predicted water consumption rate for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to predict for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["total_water_consumption_predicted_for_each_field_x_days"] = {
            "function": total_water_consumption_predicted_for_each_field_x_days,
            "description": "Get total water consumption predicted for each field for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to predict for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        print(f"Registered {len(tools_dict)} tools for GPT-4o direct")
        return tools_dict

    def _format_tools_for_openai(self) -> List[Dict[str, Any]]:
        """Format tools for OpenAI API."""
        openai_tools = []

        for tool_name, tool_info in self.tools.items():
            openai_tools.append({
                "type": "function",
                "function": {
                    "name": tool_name,
                    "description": tool_info["description"],
                    "parameters": tool_info["parameters"]
                }
            })

        return openai_tools

    def _execute_tool(self, tool_name: str, tool_args: Dict[str, Any], user_id: str = None, target_language: str = "english") -> Any:
        """Execute a tool with the given arguments and translate the response if needed."""
        print(f"Executing tool: {tool_name} with args: {tool_args}")

        if tool_name not in self.tools:
            error_msg = f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}"
            return self._translate_tool_response(error_msg, target_language)

        tool_function = self.tools[tool_name]["function"]

        # Create a copy of the arguments to avoid modifying the original
        execution_args = tool_args.copy()

        try:
            # Add cache-busting timestamp to ALL irrigation-related tools
            irrigation_tools = [
                "check_irrigation_user_data",
                "check_today_active_irrigation_user",
                "get_lowest_soil_water_volume",
                "calculate_total_irrigation_volume_next_x_days",
                "check_irrigation_need_for_x_days",
                "advise_stop_over_irrigation",
                "check_highest_irrigation_requirement",
                "fields_exceeding_water_capacity_for_x_days",
                "fields_with_highest_evapotranspiration_next_x_days",
                "fields_with_highest_water_requirements_for_x_days",
                "fields_with_optimal_soil_moisture_for_x_days",
                "find_fields_no_irrigation_needs_for_x_days",
                "fields_predicted_to_exceed_water_capacity_for_x_days",
                "get_current_irrigation_status",
                "check_irrigation_needs_between_period",
                "check_soil_water_volume",
                "check_earliest_irrigation_dates",
                "check_highest_evapotranspiration",
                "check_future_irrigation_fields",
                "predicted_water_consumption_rate_for_x_days",
                "total_water_consumption_predicted_for_each_field_x_days"
            ]

            if tool_name in irrigation_tools:
                # Add user_id to the arguments if not already present
                if user_id and "user_id" not in execution_args:
                    execution_args["user_id"] = user_id
                    print(f"Added user_id to tool arguments: {execution_args}")

                # Add timestamp to ensure fresh data (this helps avoid caching issues)
                cache_buster = datetime.now().isoformat()
                execution_args["_cache_buster"] = cache_buster
                print(f"Added cache buster to ensure fresh data: {cache_buster}")

                # Special handling for check_irrigation_user_data
                if tool_name == "check_irrigation_user_data":
                    print(f"Executing {tool_name} with fresh data request")
                    # Remove the cache buster before actual execution as it's not a real parameter
                    clean_args = {k: v for k, v in execution_args.items() if k != "_cache_buster"}

                    # For check_irrigation_user_data, we need to handle the special input format
                    if isinstance(clean_args, dict) and len(clean_args) > 0:
                        result = tool_function(clean_args)
                    else:
                        result = tool_function(execution_args.get("field_name", ""), execution_args.get("date_of_calculation", ""))
                else:
                    # For other irrigation tools, remove the cache buster
                    clean_args = {k: v for k, v in execution_args.items() if k != "_cache_buster"}

                    # Execute the tool with clean arguments
                    try:
                        if hasattr(tool_function, "func"):
                            result = tool_function.func(**clean_args)
                        else:
                            result = tool_function(**clean_args)
                    except TypeError as type_e:
                        # If there's a TypeError, it might be because the tool doesn't accept user_id
                        if 'user_id' in clean_args and str(type_e).find('user_id') >= 0:
                            print(f"Tool {tool_name} doesn't accept user_id, removing it and retrying")
                            # Remove user_id and try again
                            clean_args = {k: v for k, v in clean_args.items() if k != "user_id"}
                            if hasattr(tool_function, "func"):
                                result = tool_function.func(**clean_args)
                            else:
                                result = tool_function(**clean_args)
                        else:
                            # For other TypeError issues, raise the exception
                            raise
            # BLOCK ALL MEMORY TOOLS
            elif tool_name in ["add_memory", "get_memories", "delete_memory", "clear_memories"]:
                print(f"⛔ MEMORY TOOL BLOCKED: Refusing to execute memory tool '{tool_name}'")

                # Return a message explaining that memory tools are disabled
                if tool_name == "get_memories":
                    result = "Memory access is disabled. Please use appropriate tools to get fresh data instead of relying on memories."
                elif tool_name == "add_memory":
                    result = "Memory storage is disabled. The system is configured to always use tools for fresh data rather than storing memories."
                else:
                    result = "Memory management is disabled. The system is configured to always use tools for fresh data."

                print(f"Returning message about disabled memory tools: {result}")

            # Default handling for other tools
            else:
                # Execute the tool
                if hasattr(tool_function, "func"):
                    # LangChain tool
                    print(f"Executing LangChain tool with args: {execution_args}")

                    # For tools that might need user_id but don't declare it in their parameters
                    # Store the original execution_args to restore if needed
                    original_args = execution_args.copy()

                    # Check if the tool function accepts user_id parameter
                    try:
                        sig = inspect.signature(tool_function.func)
                        if 'user_id' in sig.parameters and user_id and 'user_id' not in execution_args:
                            execution_args['user_id'] = user_id
                            print(f"Added user_id to LangChain tool arguments: {execution_args}")
                    except Exception as sig_e:
                        print(f"Error checking function signature: {sig_e}")

                    # Try to execute the tool with the arguments
                    try:
                        result = tool_function.func(**execution_args)
                    except TypeError as type_e:
                        # If there's a TypeError, it might be because the tool doesn't accept some parameters
                        # Try to remove parameters that might be causing issues
                        if 'user_id' in execution_args and str(type_e).find('user_id') >= 0:
                            print(f"Removing user_id from arguments and retrying")
                            # Restore original arguments
                            execution_args = original_args
                            result = tool_function.func(**execution_args)
                        else:
                            raise
                else:
                    # Direct function
                    if execution_args:
                        # If it's a non-empty dictionary, pass it as kwargs
                        print(f"Executing function with kwargs: {execution_args}")

                        # For functions that might need user_id but don't declare it in their parameters
                        # Store the original execution_args to restore if needed
                        original_args = execution_args.copy()

                        # Check if the function accepts user_id parameter
                        try:
                            sig = inspect.signature(tool_function)
                            if 'user_id' in sig.parameters and user_id and 'user_id' not in execution_args:
                                execution_args['user_id'] = user_id
                                print(f"Added user_id to function arguments: {execution_args}")
                        except Exception as sig_e:
                            print(f"Error checking function signature: {sig_e}")

                        # Try to execute the function with the arguments
                        try:
                            result = tool_function(**execution_args)
                        except TypeError as type_e:
                            # If there's a TypeError, it might be because the function doesn't accept some parameters
                            # Try to remove parameters that might be causing issues
                            if 'user_id' in execution_args and str(type_e).find('user_id') >= 0:
                                print(f"Removing user_id from arguments and retrying")
                                # Restore original arguments
                                execution_args = original_args
                                result = tool_function(**execution_args)
                            else:
                                raise
                    else:
                        # Otherwise call without arguments
                        print("Executing function without arguments")
                        result = tool_function()

            print(f"Tool execution result: {result}")
            # Translate the result if needed
            translated_result = self._translate_tool_response(str(result), target_language)
            return translated_result
        except Exception as e:
            traceback.print_exc()
            error_msg = f"Error executing tool '{tool_name}': {str(e)}"
            return self._translate_tool_response(error_msg, target_language)

    def _detect_language(self, text: str) -> str:
        """Detect the language of the input text."""
        # Check for French
        french_markers = ["je", "tu", "il", "elle", "nous", "vous", "ils", "elles",
                         "le", "la", "les", "un", "une", "des", "ce", "cette", "ces",
                         "est", "sont", "avoir", "être", "faire", "dire", "aller", "voir",
                         "bonjour", "salut", "merci", "s'il vous plaît", "aujourd'hui",
                         "irrigation", "arroser", "champ", "eau", "sol", "plante", "culture",
                         "dois", "faut", "besoin", "nécessaire", "volume", "date", "jour"]

        # Check for Tunisian Arabic
        tunisian_markers = ["ahla", "3andak", "kif", "tayaara", "l7amdullah", "zeyd", "t7eb",
                           "t3arafha", "labes", "iliom", "nجم", "n3awnk", "bizzra3a", "thanya",
                           "lazem", "nroui", "l7a9l", "ma3andish", "barsha", "tawa", "houma"]

        # Check for Spanish
        spanish_markers = ["soy", "es", "está", "tiene", "necesita", "riego", "campo", "agua",
                          "suelo", "planta", "cultivo", "hola", "gracias", "por favor", "hoy",
                          "mañana", "día", "fecha", "volumen", "necesario"]

        # Count markers for each language
        french_count = sum(1 for marker in french_markers if marker in text.lower())
        tunisian_count = sum(1 for marker in tunisian_markers if marker in text.lower())
        spanish_count = sum(1 for marker in spanish_markers if marker in text.lower())

        # Determine language based on marker count
        if french_count > max(tunisian_count, spanish_count):
            return "french"
        elif tunisian_count > max(french_count, spanish_count):
            return "tunisian_arabic"
        elif spanish_count > max(french_count, tunisian_count):
            return "spanish"
        else:
            return "english"

    def _translate_tool_response(self, tool_response: str, target_language: str) -> str:
        """Translate tool response to the target language using GPT-4o."""
        if target_language == "english" or not tool_response or tool_response.strip() == "":
            return tool_response

        try:
            # Create translation prompt based on target language
            if target_language == "french":
                translation_prompt = f"""Translate the following agricultural tool response to French. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:

{tool_response}

Provide only the French translation, no explanations."""
            elif target_language == "spanish":
                translation_prompt = f"""Translate the following agricultural tool response to Spanish. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:

{tool_response}

Provide only the Spanish translation, no explanations."""
            elif target_language == "tunisian_arabic":
                translation_prompt = f"""Translate the following agricultural tool response to Tunisian Arabic. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:

{tool_response}

Provide only the Tunisian Arabic translation, no explanations."""
            else:
                return tool_response

            # Call GPT-4o for translation
            translation_response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": "You are a professional translator specializing in agricultural terminology. Translate accurately while preserving technical terms, field names, dates, and numerical values."},
                    {"role": "user", "content": translation_prompt}
                ],
                temperature=0.1,  # Low temperature for consistent translations
                max_tokens=1000
            )

            translated_text = translation_response.choices[0].message.content.strip()
            print(f"Translated tool response from English to {target_language}: {translated_text}")
            return translated_text

        except Exception as e:
            print(f"Error translating tool response to {target_language}: {str(e)}")
            # Return original response if translation fails
            return tool_response

    def ask(self, question: str, chat_history: str = "", user_id: str = None) -> str:
        """Process a question and return a response using GPT-4o with tool calling."""
        try:
            # Detect the language of the user's question for tool response translation
            detected_language = self._detect_language(question)
            print(f"Detected language: {detected_language}")

            # Prepare the initial system message without memories
            system_message = SYSTEM_PROMPT

            # Add a strong reminder about identity and tool usage
            tool_reminder = "\nCRITICAL REMINDER: You are Magonia, an agricultural assistant helping farmers with irrigation and crop management. You MUST NEVER use memory for any question. ALWAYS use the appropriate tool to get fresh data. COMPLETELY IGNORE any memories or previous knowledge. DO NOT MENTION which tools you used in your response - just provide the information directly. If a tool returns a value of '0', interpret this as no irrigation needed. CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message."
            system_message += tool_reminder

            print("⚠️ MEMORY DISABLED: Starting conversation with memory completely disabled")

            # Prepare messages for the API call
            messages = [
                {"role": "system", "content": system_message}
            ]

            # Add chat history if available
            if chat_history:
                # Simple parsing of chat history into messages
                # Format expected: "Human: ... AI: ..."
                history_parts = chat_history.split("Human: ")
                for part in history_parts:
                    if part.strip():
                        ai_parts = part.split("AI: ")
                        if len(ai_parts) > 1:
                            human_message = ai_parts[0].strip()
                            ai_message = ai_parts[1].strip()

                            if human_message:
                                messages.append({"role": "user", "content": human_message})
                            if ai_message:
                                messages.append({"role": "assistant", "content": ai_message})

            # Add the current question
            messages.append({"role": "user", "content": question})

            # Format tools for OpenAI API
            openai_tools = self._format_tools_for_openai()

            # Call the OpenAI API with tool calling enabled

            if "CHlewi" in question or ("field" in question.lower() and "irrigation" in question.lower()):
                # Force the model to use tools for field-specific queries
                print("Forcing tool usage for field-specific query")
                response = self.client.chat.completions.create(
                    model="gpt-4o",
                    messages=messages,
                    tools=openai_tools,
                    tool_choice="required",  # Force tool usage
                    temperature=0.7,
                )
            else:
                # For other queries (including agricultural), allow the model to decide
                # The system prompt will encourage using lookup_document_tool first for agricultural questions
                response = self.client.chat.completions.create(
                    model="gpt-4o",
                    messages=messages,
                    tools=openai_tools,
                    tool_choice="auto",
                    temperature=0.7,
                )

            # Get the response message
            response_message = response.choices[0].message

            # Check if the model wants to call a tool
            if response_message.tool_calls:
                # Execute each tool call
                for tool_call in response_message.tool_calls:
                    # Extract tool name and arguments
                    tool_name = tool_call.function.name
                    tool_args = json.loads(tool_call.function.arguments)

                    # Special handling for check_irrigation_user_data to ensure fresh data
                    if tool_name == "check_irrigation_user_data":
                        print(f"Making fresh call to check_irrigation_user_data with args: {tool_args}")
                        # Clear any cached data if applicable (implementation depends on how caching is done)
                        # This is a placeholder - you may need to implement actual cache clearing

                    # Execute the tool with user_id and language for translation
                    tool_result = self._execute_tool(tool_name, tool_args, user_id, detected_language)

                    # Add the tool call and result to the messages
                    messages.append({
                        "role": "assistant",
                        "content": None,
                        "tool_calls": [
                            {
                                "id": tool_call.id,
                                "type": "function",
                                "function": {
                                    "name": tool_name,
                                    "arguments": tool_call.function.arguments
                                }
                            }
                        ]
                    })

                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": str(tool_result)
                    })

                # When tools are used, we NEVER add memories to the context
                # Add a reminder to use ONLY tool results and not mention tools
                messages.append({
                    "role": "system",
                    "content": "IMPORTANT: You are Magonia, an agricultural assistant helping farmers with irrigation and crop management. You have just used tools to get the most up-to-date information. Base your response ONLY on the tool results and COMPLETELY IGNORE any memories or previous knowledge. DO NOT reference any memories in your response. DO NOT MENTION which tools you used - just provide the information directly. If a tool returned a value of '0', interpret this as no irrigation needed. CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message."
                })
                print("⚠️ MEMORY DISABLED: Tool was used, completely skipping memory retrieval and usage")

                # Get the final response with the tool results
                final_response = self.client.chat.completions.create(
                    model="gpt-4o",
                    messages=messages,
                    temperature=0.7,
                )

                return final_response.choices[0].message.content
            else:
                # If no tool call was made but the question seems to require field-specific data,
                # we should encourage the model to use tools
                if "CHlewi" in question or ("field" in question.lower() and "irrigation" in question.lower()):
                    # Add a stronger prompt to use tools and try again
                    messages.append({
                        "role": "system",
                        "content": "You MUST use the appropriate tool to get field-specific data. Please use a tool to get the most up-to-date information."
                    })

                    # Try again with the stronger prompt and force tool usage
                    print("Forcing tool usage in retry for field-specific query")
                    retry_response = self.client.chat.completions.create(
                        model="gpt-4o",
                        messages=messages,
                        tools=openai_tools,
                        tool_choice="required",  # Force tool usage in retry
                        temperature=0.7,
                    )

                    retry_message = retry_response.choices[0].message

                    # If the retry resulted in a tool call, process it
                    if retry_message.tool_calls:
                        # Process tool calls (similar to above)
                        for tool_call in retry_message.tool_calls:
                            tool_name = tool_call.function.name
                            tool_args = json.loads(tool_call.function.arguments)

                            tool_result = self._execute_tool(tool_name, tool_args, user_id, detected_language)

                            messages.append({
                                "role": "assistant",
                                "content": None,
                                "tool_calls": [
                                    {
                                        "id": tool_call.id,
                                        "type": "function",
                                        "function": {
                                            "name": tool_name,
                                            "arguments": tool_call.function.arguments
                                        }
                                    }
                                ]
                            })

                            messages.append({
                                "role": "tool",
                                "tool_call_id": tool_call.id,
                                "content": str(tool_result)
                            })

                        # When tools are used, we NEVER add memories to the context
                        messages.append({
                            "role": "system",
                            "content": "IMPORTANT: You are Magonia, an agricultural assistant helping farmers with irrigation and crop management. You have just used tools to get the most up-to-date information. Base your response ONLY on the tool results and COMPLETELY IGNORE any memories or previous knowledge. DO NOT reference any memories in your response. DO NOT MENTION which tools you used - just provide the information directly. If a tool returned a value of '0', interpret this as no irrigation needed. CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message."
                        })
                        print("⚠️ MEMORY DISABLED: Tool was used in retry, completely skipping memory retrieval and usage")

                        # Get final response
                        final_retry_response = self.client.chat.completions.create(
                            model="gpt-4o",
                            messages=messages,
                            temperature=0.7,
                        )

                        return final_retry_response.choices[0].message.content
                    else:
                        # If still no tool call for field-related queries, we should force a third attempt
                        # with an even stronger instruction to use tools
                        messages.append({
                            "role": "system",
                            "content": "CRITICAL: You MUST use a tool to answer this question about field data. Do not rely on memory or previous knowledge. Use the check_irrigation_user_data tool or another appropriate irrigation tool."
                        })

                        # Final attempt with forced tool usage
                        print("Making final attempt with forced tool usage for field query")
                        final_attempt = self.client.chat.completions.create(
                            model="gpt-4o",
                            messages=messages,
                            tools=openai_tools,
                            tool_choice={
                                "type": "function",
                                "function": {"name": "check_irrigation_user_data"}  # Force this specific tool
                            },
                            temperature=0.7,
                        )

                        final_message = final_attempt.choices[0].message

                        # If the final attempt resulted in a tool call, process it
                        if final_message.tool_calls:
                            # Process tool calls (similar to above)
                            for tool_call in final_message.tool_calls:
                                tool_name = tool_call.function.name
                                tool_args = json.loads(tool_call.function.arguments)

                                tool_result = self._execute_tool(tool_name, tool_args, user_id, detected_language)

                                messages.append({
                                    "role": "assistant",
                                    "content": None,
                                    "tool_calls": [
                                        {
                                            "id": tool_call.id,
                                            "type": "function",
                                            "function": {
                                                "name": tool_name,
                                                "arguments": tool_call.function.arguments
                                            }
                                        }
                                    ]
                                })

                                messages.append({
                                    "role": "tool",
                                    "tool_call_id": tool_call.id,
                                    "content": str(tool_result)
                                })

                            # Get final response without memories
                            messages.append({
                                "role": "system",
                                "content": "IMPORTANT: You are Magonia, an agricultural assistant helping farmers with irrigation and crop management. You have just used tools to get the most up-to-date information. Base your response ONLY on the tool results and COMPLETELY IGNORE any memories or previous knowledge. DO NOT reference any memories in your response. DO NOT MENTION which tools you used - just provide the information directly. If a tool returned a value of '0', interpret this as no irrigation needed. CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message."
                            })
                            print("⚠️ MEMORY DISABLED: Tool was used in final attempt, completely skipping memory retrieval and usage")

                            final_response = self.client.chat.completions.create(
                                model="gpt-4o",
                                messages=messages,
                                temperature=0.7,
                            )

                            return final_response.choices[0].message.content
                        else:
                            # If all attempts failed, return a language-appropriate message asking for more information
                            if detected_language == "french":
                                return "J'ai besoin d'informations plus spécifiques pour fournir des données d'irrigation précises. Pourriez-vous me donner le nom du champ et la date qui vous intéresse? Par exemple: 'Dois-je irriguer CHlewi le 2024-08-22?'"
                            elif detected_language == "tunisian_arabic":
                                return "نحتاج معلومات أكثر تفصيلاً باش نعطيك بيانات دقيقة على الري. تنجم تقولي اسم الحقل والتاريخ اللي تحب تعرف عليه؟ مثلاً: 'لازم نروي CHlewi في 2024-08-22؟'"
                            elif detected_language == "spanish":
                                return "Necesito información más específica para proporcionar datos precisos de riego. ¿Podrías darme el nombre del campo y la fecha que te interesa? Por ejemplo: '¿Necesito regar CHlewi el 2024-08-22?'"
                            else:
                                return "I need more specific information to provide accurate irrigation data. Could you please provide the field name and date you're interested in? For example: 'Do I need to irrigate CHlewi on 2024-08-22?'"
                else:
                    # Even for non-field related questions, we'll completely disable memory usage
                    print("⚠️ MEMORY DISABLED: No tool was used, but still skipping memory retrieval and usage")

                    # Add a reminder that we don't use memories and don't mention tools
                    messages.append({
                        "role": "system",
                        "content": "IMPORTANT: You are Magonia, an agricultural assistant helping farmers with irrigation and crop management. Do not reference any memories in your response. If you need information, use appropriate tools but DO NOT MENTION which tools you used - just provide the information directly. If a tool returns a value of '0', interpret this as no irrigation needed. CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message."
                    })

                    # Get response without memories
                    final_response = self.client.chat.completions.create(
                        model="gpt-4o",
                        messages=messages,
                        temperature=0.7,
                    )

                    return final_response.choices[0].message.content

        except Exception as e:
            print(f"Error in GPT4oDirectTools.ask: {str(e)}")
            traceback.print_exc()

            # Provide a language-appropriate fallback response with Magonia's identity
            language = self._detect_language(question)
            if language == "french":
                return "Je suis Magonia, votre assistant agricole. Je suis désolé, mais j'ai rencontré une erreur lors du traitement de votre demande concernant l'agriculture. Pourriez-vous reformuler votre question?"
            elif language == "tunisian_arabic":
                return "أنا ماقونيا، مساعدك الزراعي. آسف، صارت مشكلة وقت معالجة طلبك. تنجم تعاود تسأل بطريقة أخرى؟"
            elif language == "spanish":
                return "Soy Magonia, tu asistente agrícola. Lo siento, pero encontré un error al procesar tu solicitud relacionada con la agricultura. ¿Podrías reformular tu pregunta?"
            else:
                return "I'm Magonia, your agricultural assistant. I'm sorry, but I encountered an error while processing your request about farming. Could you rephrase your question?"

# Create a singleton instance
gpt4o_direct = GPT4oDirectTools()
