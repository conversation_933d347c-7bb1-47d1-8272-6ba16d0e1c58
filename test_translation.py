#!/usr/bin/env python3
"""
Test script to verify the translation functionality in gpt4o_direct.py
"""

def _detect_language(text: str) -> str:
    """Detect the language of the input text."""
    # Check for French
    french_markers = ["je", "tu", "il", "elle", "nous", "vous", "ils", "elles",
                     "le", "la", "les", "un", "une", "des", "ce", "cette", "ces",
                     "est", "sont", "avoir", "être", "faire", "dire", "aller", "voir",
                     "bonjour", "salut", "merci", "s'il vous plaît", "aujourd'hui",
                     "irrigation", "arroser", "champ", "eau", "sol", "plante", "culture",
                     "dois", "faut", "besoin", "nécessaire", "volume", "date", "jour"]

    # Check for Tunisian Arabic
    tunisian_markers = ["ahla", "3andak", "kif", "tayaara", "l7amdullah", "zeyd", "t7eb",
                       "t3arafha", "labes", "iliom", "nجم", "n3awnk", "bizzra3a", "thanya",
                       "lazem", "nroui", "l7a9l", "ma3andish", "barsha", "tawa", "houma"]

    # Check for Spanish
    spanish_markers = ["soy", "es", "está", "tiene", "necesita", "riego", "campo", "agua",
                      "suelo", "planta", "cultivo", "hola", "gracias", "por favor", "hoy",
                      "mañana", "día", "fecha", "volumen", "necesario"]

    # Count markers for each language
    french_count = sum(1 for marker in french_markers if marker in text.lower())
    tunisian_count = sum(1 for marker in tunisian_markers if marker in text.lower())
    spanish_count = sum(1 for marker in spanish_markers if marker in text.lower())

    # Determine language based on marker count
    if french_count > max(tunisian_count, spanish_count):
        return "french"
    elif tunisian_count > max(french_count, spanish_count):
        return "tunisian_arabic"
    elif spanish_count > max(french_count, tunisian_count):
        return "spanish"
    else:
        return "english"

def test_language_detection():
    """Test the language detection functionality."""
    print("Testing language detection...")

    # Test cases
    test_cases = [
        ("Hello, how are you?", "english"),
        ("Bonjour, comment allez-vous?", "french"),
        ("Ahla, kif halak?", "tunisian_arabic"),
        ("Hola, ¿cómo estás?", "spanish"),
        ("Do I need to irrigate my field today?", "english"),
        ("Dois-je arroser mon champ aujourd'hui?", "french"),
        ("Lazem nroui l7a9l iliom?", "tunisian_arabic"),
        ("¿Necesito regar mi campo hoy?", "spanish"),
    ]

    for text, expected in test_cases:
        detected = _detect_language(text)
        status = "✓" if detected == expected else "✗"
        print(f"{status} '{text}' -> {detected} (expected: {expected})")

if __name__ == "__main__":
    print("Testing Language Detection Functionality")
    print("=" * 50)

    test_language_detection()

    print("\nLanguage detection test completed!")
    print("\nNote: Translation functionality has been added to gpt4o_direct.py")
    print("Tool responses will now be automatically translated to match the user's question language.")
