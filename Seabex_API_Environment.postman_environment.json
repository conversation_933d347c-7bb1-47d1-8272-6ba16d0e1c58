{"id": "seabex-api-environment", "name": "Seabex API Environment", "values": [{"key": "base_url", "value": "https://back.seabex.com", "type": "default", "enabled": true}, {"key": "client_id", "value": "YOUR_SEABEX_CLIENT_ID", "type": "secret", "enabled": true}, {"key": "client_secret", "value": "YOUR_SEABEX_CLIENT_SECRET", "type": "secret", "enabled": true}, {"key": "user_id", "value": "f68381cd-a748-47bd-842c-701790b35e3c", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "type": "secret", "enabled": true}, {"key": "token_expires_at", "value": "", "type": "default", "enabled": true}, {"key": "cache_buster", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}